import * as jose from 'jose';
import bcrypt from 'bcryptjs';
import { randomBytes } from 'crypto';

// Token payload interfaces
export interface AccessTokenPayload {
  id: string;
  email: string;
  name: string;
  username: string;
  iat?: number;
  exp?: number;
  jti?: string;
}

export interface RefreshTokenPayload {
  id: string;
  jti: string;
  iat?: number;
  exp?: number;
}

// Token configuration
const ACCESS_TOKEN_EXPIRY = '15m'; // 15 minutes for better security
const REFRESH_TOKEN_EXPIRY = '7d'; // 7 days

// Get JWT secrets
function getJWTSecret(): Uint8Array {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET environment variable is not set');
  }
  return new TextEncoder().encode(secret);
}

function getRefreshTokenSecret(): Uint8Array {
  const secret = process.env.REFRESH_TOKEN_SECRET;
  if (!secret) {
    throw new Error('REFRESH_TOKEN_SECRET environment variable is not set');
  }
  return new TextEncoder().encode(secret);
}

// Generate unique JWT ID
function generateJTI(): string {
  return randomBytes(16).toString('hex');
}

// Create access token
export async function createAccessToken(payload: Omit<AccessTokenPayload, 'jti'>): Promise<string> {
  const jti = generateJTI();
  const secret = getJWTSecret();
  
  return await new jose.SignJWT({ ...payload, jti })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(ACCESS_TOKEN_EXPIRY)
    .setIssuer('socialm-app')
    .setAudience('socialm-users')
    .sign(secret);
}

// Create refresh token
export async function createRefreshToken(userId: string): Promise<{ token: string; jti: string }> {
  const jti = generateJTI();
  const secret = getRefreshTokenSecret();
  
  const token = await new jose.SignJWT({ id: userId, jti })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(REFRESH_TOKEN_EXPIRY)
    .setIssuer('socialm-app')
    .setAudience('socialm-users')
    .sign(secret);

  return { token, jti };
}

// Verify access token
export async function verifyAccessToken(token: string): Promise<AccessTokenPayload | null> {
  try {
    const secret = getJWTSecret();
    const { payload } = await jose.jwtVerify(token, secret, {
      issuer: 'socialm-app',
      audience: 'socialm-users',
    });
    
    return payload as AccessTokenPayload;
  } catch (error) {
    console.error('Access token verification failed:', error);
    return null;
  }
}

// Verify refresh token
export async function verifyRefreshToken(token: string): Promise<RefreshTokenPayload | null> {
  try {
    const secret = getRefreshTokenSecret();
    const { payload } = await jose.jwtVerify(token, secret, {
      issuer: 'socialm-app',
      audience: 'socialm-users',
    });
    
    return payload as RefreshTokenPayload;
  } catch (error) {
    console.error('Refresh token verification failed:', error);
    return null;
  }
}

// Hash refresh token for database storage
export async function hashRefreshToken(token: string): Promise<string> {
  return await bcrypt.hash(token, 12);
}

// Verify hashed refresh token
export async function verifyHashedRefreshToken(token: string, hashedToken: string): Promise<boolean> {
  try {
    return await bcrypt.compare(token, hashedToken);
  } catch (error) {
    console.error('Refresh token hash verification failed:', error);
    return false;
  }
}

// Cookie configuration
export interface CookieOptions {
  httpOnly: boolean;
  secure: boolean;
  sameSite: 'strict' | 'lax' | 'none';
  path: string;
  maxAge: number;
}

export function getAccessTokenCookieOptions(): CookieOptions {
  return {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 15 * 60, // 15 minutes in seconds
  };
}

export function getRefreshTokenCookieOptions(): CookieOptions {
  return {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  };
}

// Clear cookie options
export function getClearCookieOptions(): Omit<CookieOptions, 'maxAge'> & { expires: Date } {
  return {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    expires: new Date(0),
  };
}

// Token rotation utility
export async function rotateRefreshToken(oldToken: string, userId: string): Promise<{ token: string; jti: string } | null> {
  // Verify the old token first
  const payload = await verifyRefreshToken(oldToken);
  if (!payload || payload.id !== userId) {
    return null;
  }

  // Create new refresh token
  return await createRefreshToken(userId);
}

// Extract token from Authorization header
export function extractBearerToken(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}
